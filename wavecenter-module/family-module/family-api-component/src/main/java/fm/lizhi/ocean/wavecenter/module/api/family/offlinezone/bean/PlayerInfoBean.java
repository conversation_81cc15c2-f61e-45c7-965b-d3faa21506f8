package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 主播信息Bean
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PlayerInfoBean {
    /**
     * 主播ID
     */
    private Long playerId;

    /**
     * 主播名称
     */
    private String playerName;

    /**
     * 实名证件号码
     */
    private String idCardNumber;

    /**
     * 实名姓名
     */
    private String idName;
}
