package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 主播跳槽保护状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ProtectionStatusEnums {

    /**
     * 未上传
     */
    NOT_UPLOADED(0, "未上传"),

    /**
     * 已上传
     */
    UPLOADED(1, "已上传"),

    /**
     * 已生效
     */
    EFFECTIVE(2, "已生效"),

    /**
     * 主播同意
     */
    PLAYER_AGREED(3, "主播同意"),

    /**
     * 主播拒绝
     */
    PLAYER_REJECTED(4, "主播拒绝"),

    /**
     * 上传过期
     */
    EXPIRED(5, "上传过期");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static ProtectionStatusEnums getByCode(Integer code) {
        if (code == null) {
            return NOT_UPLOADED;
        }
        
        for (ProtectionStatusEnums status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return NOT_UPLOADED;
    }

    /**
     * 根据描述获取枚举
     *
     * @param description 状态描述
     * @return 枚举值
     */
    public static ProtectionStatusEnums getByDescription(String description) {
        if (description == null || description.trim().isEmpty()) {
            return NOT_UPLOADED;
        }
        
        for (ProtectionStatusEnums status : values()) {
            if (status.getDescription().equals(description.trim())) {
                return status;
            }
        }
        return NOT_UPLOADED;
    }

    /**
     * 检查是否为有效保护状态（主播已同意）
     *
     * @return 是否有效
     */
    public boolean isValidProtection() {
        return this == PLAYER_AGREED;
    }

    /**
     * 检查是否为过期状态
     *
     * @return 是否过期
     */
    public boolean isExpired() {
        return this == EXPIRED;
    }

    /**
     * 检查是否需要主播处理
     *
     * @return 是否需要主播处理
     */
    public boolean needPlayerAction() {
        return this == EFFECTIVE;
    }
}
