package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response;

import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.AgreementFileBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 跳槽保护-获取协议内容响应
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProtectionDetailResponse {

    /**
     * 协议ID
     */
    private Long id;

    /**
     * 协议生效开始时间
     */
    private Date agreementStartTime;

    /**
     * 协议生效结束时间
     */
    private Date agreementEndTime;

    /**
     * 协议文件列表
     */
    private List<AgreementFileBean> agreementFile;

    /**
     * 协议更新时间
     */
    private Date modifyTime;

    /**
     * 失效时间戳
     */
    private Long invalidTimestamp;

}
