package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 线下学习课堂白名单
 *
 * @date 2025-08-11 09:54:37
 */
@Table(name = "`offline_zone_learning_class_white_list`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OfflineZoneLearningClassWhiteList {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 资料ID
     */
    @Column(name= "`learning_id`")
    private Long learningId;

    /**
     * 白名单类型， 1=公会、2=厅、3=主播
     */
    @Column(name= "`type`")
    private Integer type;

    /**
     * 白名单 ID
     */
    @Column(name= "`white_id`")
    private Long whiteId;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", learningId=").append(learningId);
        sb.append(", type=").append(type);
        sb.append(", whiteId=").append(whiteId);
        sb.append("]");
        return sb.toString();
    }
}