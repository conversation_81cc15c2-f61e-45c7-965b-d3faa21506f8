package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 线下专区权益介绍配置
 *
 * @date 2025-08-11 09:54:37
 */
@Table(name = "`offline_zone_level_right_introduce`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OfflineZoneLevelRightIntroduce {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 权益ID
     */
    @Column(name= "`right_id`")
    private Long rightId;

    /**
     * 标题
     */
    @Column(name= "`title`")
    private String title;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 内容
     */
    @Column(name= "`content`")
    private String content;

    /**
     * 介绍图片URL（逗号分隔）
     */
    @Column(name= "`images`")
    private String images;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", rightId=").append(rightId);
        sb.append(", title=").append(title);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", content=").append(content);
        sb.append(", images=").append(images);
        sb.append("]");
        return sb.toString();
    }
}