package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 等级与权益关联关系
 *
 * @date 2025-08-11 09:54:37
 */
@Table(name = "`offline_zone_level_right_relation`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OfflineZoneLevelRightRelation {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 等级ID
     */
    @Column(name= "`level_id`")
    private Long levelId;

    /**
     * 权益ID
     */
    @Column(name= "`right_id`")
    private Long rightId;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", levelId=").append(levelId);
        sb.append(", rightId=").append(rightId);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}