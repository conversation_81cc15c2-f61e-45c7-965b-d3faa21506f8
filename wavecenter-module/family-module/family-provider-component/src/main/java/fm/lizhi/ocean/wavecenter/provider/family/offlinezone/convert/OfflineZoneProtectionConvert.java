package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionAgreementValidity;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionHistory;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestSubmitAgreement;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ProtectionDetailResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ProtectionSupportInfoResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.PlayerInfoBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.FamilyInfoBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyAuthBean;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneDataPlayerWeekDTO;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneProtectionAgreementValidityDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * 线下专区主播跳槽保护协议转换器
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {
                ConfigUtils.class
        }
)
public interface OfflineZoneProtectionConvert {

    OfflineZoneProtectionConvert INSTANCE = Mappers.getMapper(OfflineZoneProtectionConvert.class);



    @Mapping(target = "playerAgree", expression = "java(fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.ProtectionAgreeStatusEnums.NOT_PROCESSED.getCode())")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "archived", expression = "java(false)")
    @Mapping(target = "agreementUpdateTime", expression = "java(new java.util.Date())")
    @Mapping(target = "createTime", expression = "java(new java.util.Date())")
    @Mapping(target = "modifyTime", expression = "java(new java.util.Date())")
    @Mapping(target = "operator", source = "request.uploadUserId")
    @Mapping(target = "id", source = "id")
    OfflineZoneProtection buildProtection(RequestSubmitAgreement request, Long id);


    @Mapping(target = "createTime", expression = "java(new java.util.Date())")
    @Mapping(target = "modifyTime", expression = "java(new java.util.Date())")
    @Mapping(target = "operator", source = "request.uploadUserId")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "agreementUpdateTime", expression = "java(new java.util.Date())")
    @Mapping(target = "id", ignore = true)
     OfflineZoneProtectionHistory buildProtectionHistory(OfflineZoneProtection request, Long protectedId);

    OfflineZoneProtectionAgreementValidityDTO convertOfflineZoneProtectionAgreementValidityDto(OfflineZoneProtectionAgreementValidity validity);

    @Mapping(target = "agreementStartTime", source = "request.agreementStartTime")
    @Mapping(target = "agreementEndTime", source = "request.agreementEndTime")
    @Mapping(target = "uploadUserId", source = "request.uploadUserId")
    @Mapping(target = "agreementFileJson", source = "request.agreementFileJson")
    @Mapping(target = "agreementUpdateTime", expression = "java(new java.util.Date())")
    @Mapping(target = "modifyTime", expression = "java(new java.util.Date())")
    @Mapping(target = "operator", source = "request.uploadUserId")
    @Mapping(target = "stampSign", source = "request.stampSign")
    @Mapping(target = "id", source = "protection.id")
    @Mapping(target = "appId", source = "protection.appId")
    @Mapping(target = "familyId", source = "protection.familyId")
    @Mapping(target = "njId", source = "protection.njId")
    @Mapping(target = "playerId", source = "protection.playerId")
    OfflineZoneProtection buildUpdateProtection(OfflineZoneProtection protection, RequestSubmitAgreement request);

    /**
     * 构建协议详情响应
     *
     * @param protection             协议实体
     * @param protectionValidityDate
     * @return 协议详情响应
     */
    @Mapping(target = "invalidTimestamp", expression = "java(protectionValidityDate != null ? protection.getAgreementEndTime().getTime() : null)")
    @Mapping(target = "agreementFile", ignore = true)
    ProtectionDetailResponse buildProtectionDetailResponse(OfflineZoneProtection protection, Date protectionValidityDate);

    /**
     * 构建主播信息
     * @param playerData 主播数据
     * @param playerId 主播ID
     * @return 主播信息
     */
    @Mapping(target = "playerId", source = "playerId")
    @Mapping(target = "playerName", source = "playerData.userName")
    @Mapping(target = "idCardNumber", source = "playerData.idCardNumber")
    @Mapping(target = "idName", source = "playerData.idName")
    PlayerInfoBean buildPlayerInfo(OfflineZoneDataPlayerWeekDTO playerData, Long playerId);

    /**
     * 构建公会信息
     * @param familyAuth 公会认证信息
     * @return 公会信息
     */
    @Mapping(target = "familyId", source = "familyId")
    @Mapping(target = "familyName", source = "familyName")
    @Mapping(target = "authType", source = "authType")
    @Mapping(target = "authCompany", source = "authCompany")
    @Mapping(target = "familyUserBand", source = "familyUserBand")
    @Mapping(target = "familyUserName", source = "familyUserName")
    @Mapping(target = "familyPhotoUrl", source = "familyPhotoUrl")
    @Mapping(target = "createTime", source = "createTime")
    FamilyInfoBean buildFamilyInfo(FamilyAuthBean familyAuth);

    /**
     * 构建支撑信息响应
     * @param notice 告示内容
     * @param playerInfo 主播信息
     * @param familyInfo 公会信息
     * @return 支撑信息响应
     */
    @Mapping(target = "notice", source = "notice")
    @Mapping(target = "playerInfo", source = "playerInfo")
    @Mapping(target = "familyInfo", source = "familyInfo")
    ProtectionSupportInfoResponse buildSupportInfoResponse(String notice, PlayerInfoBean playerInfo, FamilyInfoBean familyInfo);


    OfflineZoneDataPlayerWeekDTO convertOfflineZoneDataPlayerWeekDto(OfflineZoneDataPlayerWeek latestPlayerData);
}

