package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataHallWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataHallWeekExample;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneDataHallWeekMapper;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.GetRoomDataListParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 线下专区-厅明细表-周 Dao
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneDataHallWeekDao {

    @Autowired
    private OfflineZoneDataHallWeekMapper offlineZoneDataHallWeekMapper;

    /**
     * 根据家族ID和厅ID查询厅数据
     *
     * @param appId         应用ID
     * @param familyId      家族ID
     * @param njId          厅主ID
     * @param startWeekDate 周开始日期
     * @param endWeekDate   周结束日期
     * @return 厅周数据
     */
    public OfflineZoneDataHallWeek getByFamilyIdAndNjIdAndWeek(Integer appId, Long familyId, Long njId, Date startWeekDate, Date endWeekDate) {
        OfflineZoneDataHallWeekExample example = new OfflineZoneDataHallWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andNjIdEqualTo(njId)
                .andStartWeekDateEqualTo(startWeekDate)
                .andEndWeekDateEqualTo(endWeekDate);
        
        List<OfflineZoneDataHallWeek> list = offlineZoneDataHallWeekMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 根据家族ID查询所有厅数据
     *
     * @param appId         应用ID
     * @param familyId      家族ID
     * @param startWeekDate 周开始日期
     * @param endWeekDate   周结束日期
     * @return 厅周数据列表
     */
    public List<OfflineZoneDataHallWeek> getByFamilyIdAndWeek(Integer appId, Long familyId, Date startWeekDate, Date endWeekDate) {
        OfflineZoneDataHallWeekExample example = new OfflineZoneDataHallWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andStartWeekDateEqualTo(startWeekDate)
                .andEndWeekDateEqualTo(endWeekDate);
        
        return offlineZoneDataHallWeekMapper.selectByExample(example);
    }

    /**
     * 根据厅ID查询最新的厅数据
     *
     * @param appId    应用ID
     * @param familyId
     * @param njId     厅主ID
     * @return 最新的厅周数据
     */
    public OfflineZoneDataHallWeek getLastWeekByNjId(Integer appId, Long familyId, Long njId) {
        OfflineZoneDataHallWeekExample example = new OfflineZoneDataHallWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andNjIdEqualTo(njId)
                .andStartWeekDateEqualTo(MyDateUtil.getLastWeekStartDay())
        ;
        example.setOrderByClause("start_week_date DESC LIMIT 1");
        
        List<OfflineZoneDataHallWeek> list = offlineZoneDataHallWeekMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 根据厅ID查询上一周的厅数据
     *
     * @param appId                应用ID
     * @param njId                 厅主ID
     * @param currentStartWeekDate 当前周开始日期
     * @return 上一周的厅数据
     */
    public OfflineZoneDataHallWeek getPreviousWeekByNjId(Integer appId, Long njId, Date currentStartWeekDate) {
        OfflineZoneDataHallWeekExample example = new OfflineZoneDataHallWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andNjIdEqualTo(njId)
                .andStartWeekDateEqualTo(MyDateUtil.getLastWeekStart(currentStartWeekDate));
        example.setOrderByClause("start_week_date DESC LIMIT 1");
        
        List<OfflineZoneDataHallWeek> list = offlineZoneDataHallWeekMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 根据家族ID查询线下厅数据（用于地图展示）
     *
     * @param appId         应用ID
     * @param familyId      家族ID
     * @param startWeekDate 周开始日期
     * @param endWeekDate   周结束日期
     * @return 线下厅数据列表
     */
    public List<OfflineZoneDataHallWeek> getOfflineHallsByFamilyIdAndWeek(Integer appId, Long familyId, Date startWeekDate, Date endWeekDate) {
        OfflineZoneDataHallWeekExample example = new OfflineZoneDataHallWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andStartWeekDateEqualTo(startWeekDate)
                .andEndWeekDateEqualTo(endWeekDate)
//                .andCategoryEqualTo(OfflineZoneRoomCategoryEnums.OFFLINE_HALL.getCategory())
                ;

        return offlineZoneDataHallWeekMapper.selectByExample(example);
    }

    /**
     * 根据条件查询厅数据列表（支持分页和排序）
     *
     * @param queryDTO 查询DTO
     * @return 分页厅数据列表
     */
    public PageList<OfflineZoneDataHallWeek> getRoomDataList(GetRoomDataListParam queryDTO) {
        // 参数校验和默认值设置
        int pageNumber = queryDTO.getPageNo() != null && queryDTO.getPageNo() > 0 ? queryDTO.getPageNo() : 1;
        int pageSize = queryDTO.getPageSize() != null && queryDTO.getPageSize() > 0 ? queryDTO.getPageSize() : 20;

        // 限制页大小
        if (pageSize > 100) {
            pageSize = 100;
        }
        OfflineZoneDataHallWeekExample example = new OfflineZoneDataHallWeekExample();
        OfflineZoneDataHallWeekExample.Criteria criteria = example.createCriteria();

        // 必须条件
        criteria.andAppIdEqualTo(queryDTO.getAppId());

        // 可选条件
        if (queryDTO.getNjId() != null) {
            criteria.andNjIdEqualTo(queryDTO.getNjId());
        }

        if (queryDTO.getCategory() != null) {
            criteria.andCategoryEqualTo(queryDTO.getCategory());
        }

        if (StringUtils.hasText(queryDTO.getProvince())) {
            criteria.andProvinceEqualTo(queryDTO.getProvince());
        }

        if (StringUtils.hasText(queryDTO.getCity())) {
            criteria.andCityEqualTo(queryDTO.getCity());
        }

        // 日期范围条件
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            Date start = DateUtil.formatStrToDate(queryDTO.getStartDate(), DateUtil.date_2);
            criteria.andStartWeekDateGreaterThanOrEqualTo(start);
        }

        if (StringUtils.hasText(queryDTO.getEndDate())) {
            Date end =  DateUtil.formatStrToDate(queryDTO.getEndDate(), DateUtil.date_2);
            criteria.andEndWeekDateLessThanOrEqualTo(end);
        }

        // 排序条件
        String orderByClause = buildOrderByClause(queryDTO.getOrderMetrics(), queryDTO.getOrderType());
        if (StringUtils.hasText(orderByClause)) {
            example.setOrderByClause(orderByClause);
        } else {
            // 默认排序
            example.setOrderByClause("start_week_date DESC");
        }

        return offlineZoneDataHallWeekMapper.pageByExample(example, pageNumber, pageSize);
    }

    /**
     * 构建排序子句
     *
     * @param orderMetrics 排序字段
     * @param orderType    排序类型
     * @return 排序子句
     */
    private String buildOrderByClause(String orderMetrics, String orderType) {
        if (!StringUtils.hasText(orderMetrics)) {
            return null;
        }

        // 排序类型，默认为 DESC
        String order = "ASC".equalsIgnoreCase(orderType) ? "ASC" : "DESC";

        // 支持的排序字段映射
        String columnName;
        switch (orderMetrics) {
            case "income":
                columnName = "income";
                break;
            case "offlinePlayerCnt":
                columnName = "offline_player_cnt";
                break;
            case "protectedPlayerCnt":
                columnName = "protected_player_cnt";
                break;
            case "offlinePlayerIncome":
                columnName = "offline_player_income";
                break;
            case "beginSignTime":
                columnName = "begin_sign_time";
                break;
            case "startWeekDate":
                columnName = "start_week_date";
                break;
            default:
                log.warn("不支持的排序字段: {}", orderMetrics);
                return null;
        }

        return columnName + " " + order;
    }
}
