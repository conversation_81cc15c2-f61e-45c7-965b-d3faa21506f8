package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import cn.hutool.core.util.StrUtil;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataFamilyWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataHallWeek;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.MapDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorFamilySummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorRoomSummary;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneDataMonitorConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneDataFamilyWeekDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneDataHallWeekDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneDataPlayerWeekDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 线下数据监控Manager
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneDataMonitorManager {

    @Autowired
    private OfflineZoneDataFamilyWeekDao offlineZoneDataFamilyWeekDao;

    @Autowired
    private OfflineZoneDataHallWeekDao offlineZoneDataHallWeekDao;

    @Autowired
    private OfflineZoneDataPlayerWeekDao offlineZoneDataPlayerWeekDao;

    /**
     * 获取家族数据监控汇总
     *
     * @param appId    应用ID
     * @param familyId 家族ID
     * @return 家族数据监控汇总
     */
    public ResponseDataMonitorFamilySummary getFamilySummary(Integer appId, Long familyId) {
        // 获取当前周和上周的家族数据
        OfflineZoneDataFamilyWeek currentWeek = offlineZoneDataFamilyWeekDao.getLastWeekByFamilyId(appId, familyId);
        if (currentWeek == null) {
            log.warn("未找到家族数据, appId:{}, familyId:{}", appId, familyId);
            return OfflineZoneDataMonitorConvert.INSTANCE.buildEmptyFamilySummary();
        }

        OfflineZoneDataFamilyWeek previousWeek = offlineZoneDataFamilyWeekDao.getPreviousWeekByFamilyId(appId, familyId, currentWeek.getStartWeekDate());
        // 构建响应
        return OfflineZoneDataMonitorConvert.INSTANCE.buildFamilySummaryResponse(currentWeek, previousWeek);

    }

    /**
     * 获取线下厅数据监控汇总
     *
     * @param appId    应用ID
     * @param familyId 家族ID
     * @param roomId   线下厅ID
     * @return 线下厅数据监控汇总
     */
    public ResponseDataMonitorRoomSummary getRoomSummary(Integer appId, Long familyId, Long roomId) {
        // 获取当前周和上周的厅数据
        OfflineZoneDataHallWeek currentWeek = offlineZoneDataHallWeekDao.getLastWeekByNjId(appId,familyId, roomId);
        if (currentWeek == null) {
            log.warn("未找到厅数据, appId:{}, familyId:{}, roomId:{}", appId, familyId, roomId);
            return OfflineZoneDataMonitorConvert.INSTANCE.buildEmptyRoomSummary();
        }

        OfflineZoneDataHallWeek previousWeek = offlineZoneDataHallWeekDao.getPreviousWeekByNjId(appId, roomId, currentWeek.getStartWeekDate());

        // 构建响应
        return OfflineZoneDataMonitorConvert.INSTANCE.buildRoomSummaryResponse(currentWeek, previousWeek);
    }

    /**
     * 获取地图数据
     *
     * @param appId    应用ID
     * @param familyId 家族ID
     * @return 地图数据列表
     */
    public List<MapDataBean> getMapData(Integer appId, Long familyId) {
        // 获取最新周期的家族数据
        OfflineZoneDataFamilyWeek familyWeek = offlineZoneDataFamilyWeekDao.getLastWeekByFamilyId(appId, familyId);
        if (familyWeek == null) {
            log.warn("未找到家族数据, appId:{}, familyId:{}", appId, familyId);
            return Collections.emptyList();
        }

        // 获取该周期的所有线下厅数据
        List<OfflineZoneDataHallWeek> hallWeeks = offlineZoneDataHallWeekDao.getOfflineHallsByFamilyIdAndWeek(
                appId, familyId, familyWeek.getStartWeekDate(), familyWeek.getEndWeekDate());

        if (hallWeeks.isEmpty()) {
            return Collections.emptyList();
        }

        // 按省份和城市分组
        Map<String, Map<String, List<OfflineZoneDataHallWeek>>> groupedData = hallWeeks.stream()
                .filter(hall -> StrUtil.isNotEmpty(hall.getProvince()) && StrUtil.isNotEmpty(hall.getCity()))
                .collect(Collectors.groupingBy(
                        OfflineZoneDataHallWeek::getProvince,
                        Collectors.groupingBy(OfflineZoneDataHallWeek::getCity)
                ));

        // 构建地图数据
        List<MapDataBean> mapDataList = new ArrayList<>();
        for (Map.Entry<String, Map<String, List<OfflineZoneDataHallWeek>>> provinceEntry : groupedData.entrySet()) {
            String province = provinceEntry.getKey();
            for (Map.Entry<String, List<OfflineZoneDataHallWeek>> cityEntry : provinceEntry.getValue().entrySet()) {
                List<OfflineZoneDataHallWeek> cityHalls = cityEntry.getValue();
                MapDataBean mapDataBean = OfflineZoneDataMonitorConvert.INSTANCE.buildMapDataBean(province, cityHalls);
                mapDataList.add(mapDataBean);
            }
        }
        return mapDataList;
    }


}
