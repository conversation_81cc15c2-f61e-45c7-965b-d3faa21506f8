package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZonePlayerDataBean;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZonePlayerDataConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneDataPlayerWeekDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneProtectionDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.GetPlayerDataListParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 线下专区主播数据管理器
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZonePlayerDataManager {

    @Autowired
    private OfflineZoneDataPlayerWeekDao offlineZoneDataPlayerWeekDao;

    @Autowired
    private OfflineZoneProtectionDao offlineZoneProtectionDao;

    @Autowired
    private UserCommonService userService;

    /**
     * 获取主播数据列表
     *
     * @param queryDTO 查询DTO
     * @return 分页主播数据列表
     */
    public PageBean<OfflineZonePlayerDataBean> getPlayerDataList(GetPlayerDataListParam queryDTO) {
        // 查询数据
        PageList<OfflineZoneDataPlayerWeek> pageList = offlineZoneDataPlayerWeekDao.getPlayerDataList(queryDTO);

        // 转换数据
        List<OfflineZonePlayerDataBean> list = OfflineZonePlayerDataConvert.INSTANCE.entityListToBeanList(pageList);

        PageBean<OfflineZonePlayerDataBean> pageBean = PageBean.of(pageList.getTotal(), list);

        // 填充用户信息
        fillUserInfo(queryDTO.getAppId(), pageBean);

        // 填充保护状态信息
        fillProtectionInfo(queryDTO, pageBean);

        // todo 需要修改，跳槽保护状态筛选，不能这么查
        return pageBean;
    }

    /**
     * 填充用户信息
     *
     * @param appId 应用ID
     * @param pageBean 分页数据
     */
    private void fillUserInfo(Integer appId, PageBean<OfflineZonePlayerDataBean> pageBean) {
        if (CollectionUtils.isEmpty(pageBean.getList())) {
            return;
        }

        // 收集所有用户ID（主播ID和厅主ID）
        Set<Long> userIds = new HashSet<>();
        pageBean.getList().forEach(playerDataBean -> {
            if (playerDataBean.getUserId() != null) {
                userIds.add(playerDataBean.getUserId());
            }
            if (playerDataBean.getNjId() != null) {
                userIds.add(playerDataBean.getNjId());
            }
        });

        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        try {
            // 批量获取用户信息
            Map<Long, UserBean> userMap = getUserMap(appId, userIds);

            // 填充用户信息
            pageBean.getList().forEach(playerDataBean -> {
                // 填充主播信息
                UserBean userBean = userMap.get(playerDataBean.getUserId());
                if (userBean != null) {
                    playerDataBean.setUserInfo(userBean);
                }

                // 填充厅主信息
                UserBean njBean = userMap.get(playerDataBean.getNjId());
                if (njBean != null) {
                    playerDataBean.setNjInfo(njBean);
                }
            });
        } catch (Exception e) {
            log.warn("获取用户信息失败, appId={}, userIds={}", appId, userIds, e);
        }
    }

    /**
     * 填充保护状态信息
     *
     * @param param 查询参数
     * @param pageBean 分页数据
     */
    private void fillProtectionInfo(GetPlayerDataListParam param, PageBean<OfflineZonePlayerDataBean> pageBean) {
        if (CollectionUtils.isEmpty(pageBean.getList())) {
            return;
        }

        // 收集所有主播ID
        Set<Long> playerIds = pageBean.getList().stream()
                .map(OfflineZonePlayerDataBean::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(playerIds)) {
            return;
        }

        try {
            // 批量获取保护信息
            // todo 需要修改为从 offlineZoneProtectionManager中查询并聚合状态
            Map<Long, OfflineZoneProtection> protectionMap =
                    offlineZoneProtectionDao.getProtectionMapByPlayerIds(param.getAppId(), param.getFamilyId(), playerIds);

            // 填充保护状态
            pageBean.getList().forEach(playerDataBean -> {
                OfflineZoneProtection protection = protectionMap.get(playerDataBean.getUserId());
                if (protection != null) {
                    playerDataBean.setProtection(true);
//                    playerDataBean.setProtectionStatus(getProtectionStatusDesc(protection));
                } else {
                    playerDataBean.setProtection(false);
//                    playerDataBean.setProtectionStatus("未上传");
                }
            });
        } catch (Exception e) {
            log.warn("获取保护状态信息失败, appId={}, familyId={}, playerIds={}", param.getPlayerId(),param.getFamilyId(), playerIds, e);
        }
    }


    /**
     * 批量获取用户信息
     *
     * @param appId 应用ID
     * @param userIds 用户ID集合
     * @return 用户信息映射
     */
    private Map<Long, UserBean> getUserMap(Integer appId, Set<Long> userIds) {
        Result<List<UserBean>> result = userService.getUserByIds(appId, new ArrayList<>(userIds));
        if (RpcResult.isSuccess(result)) {
            return result.target().stream()
                    .collect(Collectors.toMap(UserBean::getId, userBean -> userBean, (a, b) -> a));
        }
        return Collections.emptyMap();
    }
}
