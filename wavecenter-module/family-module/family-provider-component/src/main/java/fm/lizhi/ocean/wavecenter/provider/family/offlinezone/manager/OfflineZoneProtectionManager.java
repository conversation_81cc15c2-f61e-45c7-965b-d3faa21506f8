package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import cn.hutool.core.date.DateUtil;
import fm.lizhi.common.datastore.mysql.generator.IdGenerator;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.*;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestPlayerHandleAgreement;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestSubmitAgreement;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.config.OfflineZoneFamilyConfig;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneProtectionConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneDataFamilyWeekDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneDataHallWeekDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneDataPlayerWeekDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneProtectionDao;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyAuthBean;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneDataPlayerWeekDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.ProtectionAgreeStatusEnums;

import static fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneProtectionService.*;

/**
 * 线下专区主播跳槽保护协议管理器
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneProtectionManager {

    @Autowired
    private OfflineZoneProtectionDao offlineZoneProtectionDao;

    @Autowired
    private GuidGenerator idGenerator;

    @Autowired
    private OfflineZoneDataPlayerWeekDao offlineZoneDataPlayerWeekDao;

    @Autowired
    private OfflineZoneFamilyConfig offlineZoneFamilyConfig;

    @Autowired
    private UserFamilyService userFamilyService;


    /**
     * 查询用户上传协议时效
     */
    public Date getProtectionValidityDate(int appId, Long familyId, Long njId, Long playerId){
        OfflineZoneProtectionAgreementValidity validity = offlineZoneProtectionDao.getProtectionAgreementValidity(appId, familyId, njId, playerId);
        if (validity != null){
            // 先从平台的时效库里面查询
            return validity.getExpiredTime();
        }else {
            // 再从大数据表中查询签约时间 + 30 天
            OfflineZoneDataPlayerWeek latestPlayerData = offlineZoneDataPlayerWeekDao.getLatestPlayerData(appId, familyId, njId, playerId);
            return latestPlayerData == null ? null : DateUtil.offsetDay(latestPlayerData.getBeginSignTime(), offlineZoneFamilyConfig.getProtectionValidityDay()) ;
        }
    }


    /**
     * 创建协议
     */
    public Result<Void> createProtection(RequestSubmitAgreement request) {

        // 先检查协议是否已经受保护
        Boolean isExist = offlineZoneProtectionDao.existProtection(request.getAppId(), request.getFamilyId(), request.getNjId(), request.getPlayerId());
        if (isExist) {
            log.info("协议已存在, familyId={}, njId={}, playerId={}", request.getFamilyId(), request.getNjId(), request.getPlayerId());
            return new Result<>(SUBMIT_AGREEMENT_EXIST, null);
        }


        Long id = idGenerator.genId();

        // 构建协议记录
        OfflineZoneProtection protection = OfflineZoneProtectionConvert.INSTANCE.buildProtection(request, id);

        // 构建协议历史记录
        OfflineZoneProtectionHistory protectionHistory = OfflineZoneProtectionConvert.INSTANCE.buildProtectionHistory(protection, id);

        try {
            // 保存协议和历史记录
            offlineZoneProtectionDao.saveProtection(protection, protectionHistory);

            // todo 发送消息给主播
        }catch (Exception e) {
            log.error("保存协议失败, familyId={}, njId={}, playerId={}", request.getFamilyId(), request.getNjId(), request.getPlayerId(), e);
            return new Result<>(SUBMIT_AGREEMENT_FAIL, null);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    /**
     * 更新协议
     */
    public Result<Void> updateProtection(RequestSubmitAgreement request) {

        if (request == null || request.getId() == null) {
            return new Result<>(SUBMIT_AGREEMENT_PARAM_ERROR, null);
        }

        OfflineZoneProtection protection = offlineZoneProtectionDao.getProtectionById(request.getId());
        if (protection == null) {
            log.info("协议不存在, id={}", request.getId());
            return new Result<>(SUBMIT_AGREEMENT_NOT_EXIST, null);
        }

        protection = OfflineZoneProtectionConvert.INSTANCE.buildUpdateProtection(protection, request);

        // 构建协议历史记录
        OfflineZoneProtectionHistory protectionHistory = OfflineZoneProtectionConvert.INSTANCE.buildProtectionHistory(protection, protection.getId());

        try {
            offlineZoneProtectionDao.updateProtection(protection, protectionHistory);
        } catch (Exception e) {
            log.error("更新协议失败, familyId={}, njId={}, playerId={}", request.getFamilyId(), request.getNjId(), request.getPlayerId(), e);
            return new Result<>(SUBMIT_AGREEMENT_FAIL, null);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    /**
     * 主播处理协议（同意或拒绝）
     */
    public Result<Void> playerHandleProtection(RequestPlayerHandleAgreement request) {

        // 校验同意状态是否有效
        ProtectionAgreeStatusEnums agreeStatusEnum = ProtectionAgreeStatusEnums.getByCode(request.getAgreeStatus());
        if (agreeStatusEnum == ProtectionAgreeStatusEnums.NOT_PROCESSED) {
            log.info("主播处理协议参数错误, 无效的同意状态: {}, protectionId:{}", request.getAgreeStatus(), request.getProtectionId());
            return new Result<>(PLAYER_HANDLE_PARAM_ERROR, null);
        }

        // 2. 查询协议是否存在
        OfflineZoneProtection protection = offlineZoneProtectionDao.getProtectionById(request.getProtectionId());
        if (protection == null) {
            log.info("主播处理协议失败, 协议不存在, protectionId={}", request.getProtectionId());
            return new Result<>(PLAYER_HANDLE_PROTECTION_NOT_FOUND, null);
        }

        // 3. 校验协议是否属于该主播
        if (!protection.getPlayerId().equals(request.getPlayerId())) {
            log.info("主播处理协议失败, 协议不属于该主播, protectionId={}, playerId={}, protectionPlayerId={}",
                    request.getProtectionId(), request.getPlayerId(), protection.getPlayerId());
            return new Result<>(PLAYER_HANDLE_PROTECTION_NOT_FOUND, null);
        }


        // 4. 校验协议当前状态是否允许处理（必须是未处理状态）
        if (!ProtectionAgreeStatusEnums.NOT_PROCESSED.getCode().equals(protection.getPlayerAgree())) {
            log.info("主播处理协议失败, 协议已处理, protectionId={}, currentStatus={}",
                    request.getProtectionId(), protection.getPlayerAgree());
            return new Result<>(PLAYER_HANDLE_ALREADY_PROCESSED, null);
        }

        // 5.校验是否在时效内
        Date validDate = getProtectionValidityDate(request.getAppId(), protection.getFamilyId(), protection.getNjId(), request.getPlayerId());
        if (validDate == null || validDate.before(new Date())) {
            log.info("协议已过期, familyId={}, njId={}, playerId={}", protection.getFamilyId(), protection.getNjId(), request.getPlayerId());
            return new Result<>(PLAYER_HANDLE_EXPIRED, null);
        }

        try {

            // 5. 更新协议状态
            offlineZoneProtectionDao.updateProtectionPlayerAgree(request.getProtectionId(), request.getAgreeStatus());

            // 6. 根据主播处理结果发送通知
            if (ProtectionAgreeStatusEnums.AGREED.getCode().equals(request.getAgreeStatus())) {
                // TODO: 发送消息通知产品指定角色（主播同意协议保护）

                log.info("主播同意协议保护, protectionId={}, playerId={}", request.getProtectionId(), request.getPlayerId());
            } else if (ProtectionAgreeStatusEnums.REJECTED.getCode().equals(request.getAgreeStatus())) {
                // TODO: 发送消息通知产品指定角色（主播拒绝协议保护）
                log.info("主播拒绝协议保护, protectionId={}, playerId={}",
                        request.getProtectionId(), request.getPlayerId());
            }

        } catch (Exception e) {
            log.error("主播处理协议失败, protectionId={}, playerId={}, agreeStatus={}",
                    request.getProtectionId(), request.getPlayerId(), request.getAgreeStatus(), e);
            return new Result<>(PLAYER_HANDLE_FAIL, null);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    /**
     * 根据协议ID获取协议详情
     * @param id 协议ID
     * @return 协议详情
     */
    public OfflineZoneProtection getProtectionDetail(Long id) {
        return offlineZoneProtectionDao.getProtectionById(id);
    }

    /**
     * 获取主播身份信息
     *
     * @param playerId 主播ID
     * @param njId
     * @param familyId
     * @return 主播身份信息
     */
    public OfflineZoneDataPlayerWeekDTO getPlayerIdentityInfo(int appId, Long familyId, Long njId, Long playerId) {
        OfflineZoneDataPlayerWeek latestPlayerData = offlineZoneDataPlayerWeekDao.getLatestPlayerData(appId, familyId, njId, playerId);

        return OfflineZoneProtectionConvert.INSTANCE.convertOfflineZoneDataPlayerWeekDto(latestPlayerData);
    }

    /**
     * 获取公会认证信息
     * @param appId 应用ID
     * @param familyId 公会ID
     * @return 公会认证信息
     */
    public FamilyAuthBean getFamilyAuthInfo(int appId, long familyId) {
        Result<FamilyAuthBean> result = userFamilyService.getUserFamilyAuth(appId, familyId);
        if (result != null && result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return result.target();
        }
        return null;
    }
}
