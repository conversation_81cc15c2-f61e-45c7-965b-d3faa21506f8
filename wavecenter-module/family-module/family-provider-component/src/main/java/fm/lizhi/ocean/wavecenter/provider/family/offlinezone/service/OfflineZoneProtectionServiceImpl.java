package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.service;

import cn.hutool.core.util.StrUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetProtectionSupportInfo;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestPlayerHandleAgreement;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestSubmitAgreement;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ProtectionDetailResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ProtectionSupportInfoResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneProtectionService;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.config.CommonOfflineZoneConfig;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.config.OfflineZoneFamilyConfig;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneProtectionConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneDataPlayerWeekDTO;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneProtectionAgreementValidityDTO;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZoneProtectionManager;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyAuthBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.PlayerInfoBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.FamilyInfoBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.AgreementFileBean;
import com.alibaba.fastjson.JSON;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import cn.hutool.core.util.StrUtil;

import java.util.Date;
import java.util.Optional;
import java.util.List;

/**
 * 线下专区主播跳槽保护协议服务实现
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class OfflineZoneProtectionServiceImpl implements OfflineZoneProtectionService {

    @Autowired
    private OfflineZoneProtectionManager offlineZoneProtectionManager;

    @Autowired
    private OfflineZoneFamilyConfig offlineZoneFamilyConfig;

    @Override
    public Result<Void> submitAgreement(RequestSubmitAgreement request) {

        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));

        if (StrUtil.isEmpty(request.getAgreementFileJson())){
            return new Result<>(SUBMIT_AGREEMENT_PARAM_ERROR,  null);
        }

        // 校验是否在时效内
        Date validDate =
                offlineZoneProtectionManager.getProtectionValidityDate(request.getAppId(), request.getFamilyId(), request.getNjId(), request.getPlayerId());
        if (validDate == null || validDate.before(new Date())){
            log.info("协议已过期, familyId={}, njId={}, playerId={}, expiredTime={}",
                    request.getFamilyId(), request.getNjId(), request.getPlayerId(), validDate);
            return new Result<>(SUBMIT_AGREEMENT_EXPIRED,  null);
        }

        if (request.getId() == null){
            return offlineZoneProtectionManager.createProtection(request);
        }else {
            return offlineZoneProtectionManager.updateProtection(request);
        }
    }

    @Override
    public Result<Void> playerHandleAgreement(RequestPlayerHandleAgreement request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));

        return offlineZoneProtectionManager.playerHandleProtection(request);
    }

    @Override
    public Result<ProtectionDetailResponse> getDetail(Long id) {
        LogContext.addReqLog("id={}", id);
        LogContext.addResLog("id={}", id);

        if (id == null) {
            return new Result<>(GET_DETAIL_PARAM_ERROR, null);
        }

        OfflineZoneProtection protection = offlineZoneProtectionManager.getProtectionDetail(id);

        if (protection == null) {
            log.info("协议不存在, id={}", id);
            return new Result<>(GET_DETAIL_NOT_FOUND, null);
        }

        // 获取协议过期时间
        Date protectionValidityDate = offlineZoneProtectionManager.getProtectionValidityDate(protection.getAppId(), protection.getFamilyId(), protection.getNjId(), protection.getPlayerId());
        ProtectionDetailResponse response = OfflineZoneProtectionConvert.INSTANCE.buildProtectionDetailResponse(protection, protectionValidityDate);

        // 解析协议文件JSON
        if (StrUtil.isNotEmpty(protection.getAgreementFileJson())) {
            List<AgreementFileBean> agreementFiles = JSON.parseArray(protection.getAgreementFileJson(), AgreementFileBean.class);
            response.setAgreementFile(agreementFiles);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, response);
    }

    @Override
    public Result<ProtectionSupportInfoResponse> getSupportInfo(RequestGetProtectionSupportInfo request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));

        Long playerId = request.getPlayerId();
        Long familyId = request.getFamilyId();
        Long njId = request.getNjId();

        // 获取主播身份信息
        OfflineZoneDataPlayerWeekDTO playerData = offlineZoneProtectionManager.getPlayerIdentityInfo(request.getAppId(), familyId, njId, playerId);
        if (playerData == null) {
            return new Result<>(GET_SUPPORT_INFO_PLAYER_NOT_FOUND, null);
        }

        // 构建主播信息
        PlayerInfoBean playerInfo = OfflineZoneProtectionConvert.INSTANCE.buildPlayerInfo(playerData, playerId);

        // 获取公会认证信息
        FamilyAuthBean familyAuth = offlineZoneProtectionManager.getFamilyAuthInfo(request.getAppId(), familyId);
        FamilyInfoBean familyInfo = null;
        if (familyAuth != null) {
            // 构建公会信息
            familyInfo = OfflineZoneProtectionConvert.INSTANCE.buildFamilyInfo(familyAuth);
        }

        // 获取告示信息
        CommonOfflineZoneConfig bizConfig = offlineZoneFamilyConfig.getBizConfig(request.getAppId());
        String notice = bizConfig.getProtectionNotice();
        ProtectionSupportInfoResponse response = OfflineZoneProtectionConvert.INSTANCE.buildSupportInfoResponse(notice, playerInfo, familyInfo);

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, response);
    }
}
