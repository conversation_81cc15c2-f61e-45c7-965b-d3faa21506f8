package fm.lizhi.ocean.wavecenter.test.offlinezone;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataFamilyWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataHallWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneDataFamilyWeekMapper;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneDataHallWeekMapper;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneDataPlayerWeekMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.HyPlayerSign;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.PpPlayerSign;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.XmPlayerSign;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.HyPlayerSignMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.PpPlayerSignMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.XmPlayerSignMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.ext.HyFamilyExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.ext.PpFamilyExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.ext.XmFamilyExtMapper;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 线下专区数据初始化测试类
 *
 * <AUTHOR>
 */
@Slf4j
public class DataInitTest extends AbstractDataCenterTest {

    @Autowired
    private OfflineZoneDataFamilyWeekMapper offlineZoneDataFamilyWeekMapper;

    @Autowired
    private OfflineZoneDataHallWeekMapper offlineZoneDataHallWeekMapper;

    @Autowired
    private OfflineZoneDataPlayerWeekMapper offlineZoneDataPlayerWeekMapper;

    // Family相关Mapper
    @Autowired
    private PpFamilyExtMapper ppFamilyExtMapper;

    @Autowired
    private HyFamilyExtMapper hyFamilyExtMapper;

    @Autowired
    private XmFamilyExtMapper xmFamilyExtMapper;

    // Player相关Mapper
    @Autowired
    private PpPlayerSignMapper ppPlayerSignMapper;

    @Autowired
    private HyPlayerSignMapper hyPlayerSignMapper;

    @Autowired
    private XmPlayerSignMapper xmPlayerSignMapper;

    /**
     * 初始化线下专区周数据
     */
    @Test
    public void initOfflineZoneWeekData() {
        log.info("开始初始化线下专区周数据...");

        // 计算上周的开始和结束日期
        LocalDate today = LocalDate.now();
        LocalDate lastMonday = today.minusWeeks(1).with(DayOfWeek.MONDAY);
        LocalDate lastSunday = lastMonday.plusDays(6);

        Date startWeekDate = Date.from(lastMonday.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endWeekDate = Date.from(lastSunday.atStartOfDay(ZoneId.systemDefault()).toInstant());

        log.info("上周时间范围: {} - {}", lastMonday, lastSunday);

        // 遍历所有业务环境
        List<BusinessEvnEnum> businessEnums = Arrays.asList(
            BusinessEvnEnum.PP,
            BusinessEvnEnum.HEI_YE,
            BusinessEvnEnum.XIMI
        );

        for (BusinessEvnEnum bizEnum : businessEnums) {
            log.info("开始处理业务环境: {}", bizEnum.name());

            try {
                // 初始化Family周数据
                initFamilyWeekData(bizEnum, startWeekDate, endWeekDate);

                // 初始化Hall周数据
                initHallWeekData(bizEnum, startWeekDate, endWeekDate);

                // 初始化Player周数据
                initPlayerWeekData(bizEnum, startWeekDate, endWeekDate);

                log.info("业务环境 {} 数据初始化完成", bizEnum.name());
            } catch (Exception e) {
                log.error("业务环境 {} 数据初始化失败", bizEnum.name(), e);
            }
        }

        log.info("线下专区周数据初始化完成");
    }

    /**
     * 简化版本的初始化方法，仅使用模拟数据进行测试
     */
    @Test
    public void initOfflineZoneWeekDataSimple() {
        log.info("开始简化版线下专区周数据初始化...");

        // 计算上周的开始和结束日期
        LocalDate today = LocalDate.now();
        LocalDate lastMonday = today.minusWeeks(1).with(DayOfWeek.MONDAY);
        LocalDate lastSunday = lastMonday.plusDays(6);

        Date startWeekDate = Date.from(lastMonday.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endWeekDate = Date.from(lastSunday.atStartOfDay(ZoneId.systemDefault()).toInstant());

        log.info("上周时间范围: {} - {}", lastMonday, lastSunday);

        // 只处理PP业务环境作为示例
        BusinessEvnEnum bizEnum = BusinessEvnEnum.PP;
        log.info("开始处理业务环境: {}", bizEnum.name());

        try {
            // 使用模拟数据初始化
            initFamilyWeekDataWithMockData(bizEnum, startWeekDate, endWeekDate);
            initHallWeekDataWithMockData(bizEnum, startWeekDate, endWeekDate);
            initPlayerWeekDataWithMockData(bizEnum, startWeekDate, endWeekDate);

            log.info("简化版线下专区周数据初始化完成");
        } catch (Exception e) {
            log.error("简化版数据初始化失败", e);
        }
    }

    /**
     * 使用模拟数据初始化Family周数据
     */
    private void initFamilyWeekDataWithMockData(BusinessEvnEnum bizEnum, Date startWeekDate, Date endWeekDate) {
        log.info("使用模拟数据初始化 {} Family周数据", bizEnum.name());

        // 模拟Family ID列表
        List<Long> mockFamilyIds = Arrays.asList(1001L, 1002L, 1003L);

        for (Long familyId : mockFamilyIds) {
            OfflineZoneDataFamilyWeek familyWeek = OfflineZoneDataFamilyWeek.builder()
                .appId(bizEnum.getAppId())
                .startWeekDate(startWeekDate)
                .endWeekDate(endWeekDate)
                .familyId(familyId)
                .familyName("测试公会_" + familyId)
                .basicCnt(1)
                .offlineHallCnt(2L)
                .offlineHallCntRate(BigDecimal.valueOf(0.5))
                .offlineHallIncome(BigDecimal.valueOf(10000))
                .offlineHallIncomeRate(BigDecimal.valueOf(0.3))
                .playerCnt(10)
                .offlinePlayerCnt(5)
                .offlinePlayerCntRate(BigDecimal.valueOf(0.5))
                .protectedPlayerCnt(3)
                .protectedPlayerCntRate(BigDecimal.valueOf(0.3))
                .protectedPlayerCntRealTime(3)
                .protectedPlayerCntRateRealTime(BigDecimal.valueOf(0.3))
                .createTime(new Date())
                .modifyTime(new Date())
                .build();

            offlineZoneDataFamilyWeekMapper.insert(familyWeek);
            log.info("插入Family周数据: familyId={}", familyId);
        }
    }

    /**
     * 使用模拟数据初始化Hall周数据
     */
    private void initHallWeekDataWithMockData(BusinessEvnEnum bizEnum, Date startWeekDate, Date endWeekDate) {
        log.info("使用模拟数据初始化 {} Hall周数据", bizEnum.name());

        // 模拟Hall数据
        List<Long> mockFamilyIds = Arrays.asList(1001L, 1002L, 1003L);

        for (Long familyId : mockFamilyIds) {
            for (int i = 1; i <= 2; i++) {
                Long njId = familyId * 100 + i;

                OfflineZoneDataHallWeek hallWeek = OfflineZoneDataHallWeek.builder()
                    .appId(bizEnum.getAppId())
                    .startWeekDate(startWeekDate)
                    .endWeekDate(endWeekDate)
                    .familyId(familyId)
                    .familyName("测试公会_" + familyId)
                    .njId(njId)
                    .njName("测试厅主_" + njId)
                    .beginSignTime(new Date())
                    .category(0) // 线下
                    .country("中国")
                    .province("广东省")
                    .city("深圳市")
                    .income(BigDecimal.valueOf(5000 + i * 1000))
                    .offlinePlayerCnt(3 + i)
                    .createTime(new Date())
                    .modifyTime(new Date())
                    .build();

                offlineZoneDataHallWeekMapper.insert(hallWeek);
                log.info("插入Hall周数据: familyId={}, njId={}", familyId, njId);
            }
        }
    }

    /**
     * 使用模拟数据初始化Player周数据
     */
    private void initPlayerWeekDataWithMockData(BusinessEvnEnum bizEnum, Date startWeekDate, Date endWeekDate) {
        log.info("使用模拟数据初始化 {} Player周数据", bizEnum.name());

        // 模拟Player数据
        List<Long> mockPlayerIds = Arrays.asList(10001L, 10002L, 10003L, 10004L, 10005L);

        for (Long playerId : mockPlayerIds) {
            Long familyId = 1001L + (playerId % 3); // 分配到不同的公会
            Long njId = familyId * 100 + 1;

            OfflineZoneDataPlayerWeek playerWeek = OfflineZoneDataPlayerWeek.builder()
                .appId(bizEnum.getAppId())
                .startWeekDate(startWeekDate)
                .endWeekDate(endWeekDate)
                .userId(playerId)
                .userName("测试主播_" + playerId)
                .idCardNumber("******************")
                .idName("张三" + playerId)
                .familyId(familyId)
                .familyName("测试公会_" + familyId)
                .njId(njId)
                .njName("测试厅主_" + njId)
                .beginSignTime(new Date())
                .category(0) // 线下
                .country("中国")
                .province("广东省")
                .city("深圳市")
                .createTime(new Date())
                .modifyTime(new Date())
                .build();

            offlineZoneDataPlayerWeekMapper.insert(playerWeek);
            log.info("插入Player周数据: playerId={}, familyId={}", playerId, familyId);
        }
    }

    /**
     * 初始化Family周数据
     */
    private void initFamilyWeekData(BusinessEvnEnum bizEnum, Date startWeekDate, Date endWeekDate) {
        log.info("开始初始化 {} Family周数据", bizEnum.name());

        try {
            List<Long> familyIds = getFamilyIdsByBizEnum(bizEnum);
            log.info("获取到 {} 个Family ID", familyIds.size());

            for (Long familyId : familyIds) {
                // 构建Family周数据
                OfflineZoneDataFamilyWeek familyWeek = OfflineZoneDataFamilyWeek.builder()
                    .appId(bizEnum.getAppId())
                    .startWeekDate(startWeekDate)
                    .endWeekDate(endWeekDate)
                    .familyId(familyId)
                    .familyName("Family_" + familyId) // 模拟数据，实际应从family表获取
                    .basicCnt(1) // 模拟数据
                    .offlineHallCnt(0L) // 模拟数据
                    .offlineHallCntRate(BigDecimal.ZERO) // 模拟数据
                    .offlineHallIncome(BigDecimal.ZERO) // 模拟数据
                    .offlineHallIncomeRate(BigDecimal.ZERO) // 模拟数据
                    .playerCnt(0) // 模拟数据
                    .offlinePlayerCnt(0) // 模拟数据
                    .offlinePlayerCntRate(BigDecimal.ZERO) // 模拟数据
                    .protectedPlayerCnt(0) // 模拟数据
                    .protectedPlayerCntRate(BigDecimal.ZERO) // 模拟数据
                    .protectedPlayerCntRealTime(0) // 模拟数据
                    .protectedPlayerCntRateRealTime(BigDecimal.ZERO) // 模拟数据
                    .createTime(new Date())
                    .modifyTime(new Date())
                    .build();

                // 插入数据
                offlineZoneDataFamilyWeekMapper.insert(familyWeek);
            }

            log.info("{} Family周数据初始化完成，共处理 {} 条记录", bizEnum.name(), familyIds.size());
        } catch (Exception e) {
            log.error("{} Family周数据初始化失败", bizEnum.name(), e);
            throw e;
        }
    }

    /**
     * 初始化Hall周数据
     */
    private void initHallWeekData(BusinessEvnEnum bizEnum, Date startWeekDate, Date endWeekDate) {
        log.info("开始初始化 {} Hall周数据", bizEnum.name());

        try {
            List<Long> familyIds = getFamilyIdsByBizEnum(bizEnum);
            log.info("获取到 {} 个Family ID用于Hall数据", familyIds.size());

            for (Long familyId : familyIds) {
                // 模拟每个Family有1-3个Hall
                int hallCount = (int) (Math.random() * 3) + 1;

                for (int i = 0; i < hallCount; i++) {
                    Long njId = familyId * 1000 + i; // 模拟厅主ID

                    // 构建Hall周数据
                    OfflineZoneDataHallWeek hallWeek = OfflineZoneDataHallWeek.builder()
                        .appId(bizEnum.getAppId())
                        .startWeekDate(startWeekDate)
                        .endWeekDate(endWeekDate)
                        .familyId(familyId)
                        .familyName("Family_" + familyId) // 模拟数据
                        .njId(njId)
                        .njName("NJ_" + njId) // 模拟数据
                        .beginSignTime(new Date()) // 模拟数据
                        .category(0) // 0-线下，1-线上
                        .country("中国") // 模拟数据
                        .province("广东省") // 模拟数据
                        .city("深圳市") // 模拟数据
                        .income(BigDecimal.valueOf(Math.random() * 10000)) // 模拟收入
                        .offlinePlayerCnt((int) (Math.random() * 10) + 1) // 模拟线下主播数
                        .createTime(new Date())
                        .modifyTime(new Date())
                        .build();

                    // 插入数据
                    offlineZoneDataHallWeekMapper.insert(hallWeek);
                }
            }

            log.info("{} Hall周数据初始化完成", bizEnum.name());
        } catch (Exception e) {
            log.error("{} Hall周数据初始化失败", bizEnum.name(), e);
            throw e;
        }
    }

    /**
     * 初始化Player周数据
     */
    private void initPlayerWeekData(BusinessEvnEnum bizEnum, Date startWeekDate, Date endWeekDate) {
        log.info("开始初始化 {} Player周数据", bizEnum.name());

        try {
            List<Long> playerIds = getPlayerIdsByBizEnum(bizEnum);
            log.info("获取到 {} 个Player ID", playerIds.size());

            for (Long playerId : playerIds) {
                Long familyId = playerId / 1000; // 模拟关联的familyId
                Long njId = familyId * 1000; // 模拟关联的njId

                // 构建Player周数据
                OfflineZoneDataPlayerWeek playerWeek = OfflineZoneDataPlayerWeek.builder()
                    .appId(bizEnum.getAppId())
                    .startWeekDate(startWeekDate)
                    .endWeekDate(endWeekDate)
                    .userId(playerId)
                    .userName("Player_" + playerId) // 模拟数据
                    .idCardNumber("******************") // 模拟身份证号
                    .idName("张三" + playerId) // 模拟实名姓名
                    .familyId(familyId)
                    .familyName("Family_" + familyId) // 模拟数据
                    .njId(njId)
                    .njName("NJ_" + njId) // 模拟数据
                    .beginSignTime(new Date()) // 模拟签约时间
                    .category(0) // 0-线下，1-线上
                    .country("中国") // 模拟数据
                    .province("广东省") // 模拟数据
                    .city("深圳市") // 模拟数据
                    .createTime(new Date())
                    .modifyTime(new Date())
                    .build();

                // 插入数据
                offlineZoneDataPlayerWeekMapper.insert(playerWeek);
            }

            log.info("{} Player周数据初始化完成，共处理 {} 条记录", bizEnum.name(), playerIds.size());
        } catch (Exception e) {
            log.error("{} Player周数据初始化失败", bizEnum.name(), e);
            throw e;
        }
    }

    /**
     * 根据业务环境获取Family ID列表
     */
    private List<Long> getFamilyIdsByBizEnum(BusinessEvnEnum bizEnum) {
        Long lastFamilyId = 0L;
        Integer pageSize = 10; // 限制数量，避免数据过多

        switch (bizEnum) {
            case PP:
                return ppFamilyExtMapper.getFamilyIdsByPage(lastFamilyId, pageSize);
            case HEI_YE:
                return hyFamilyExtMapper.getFamilyIdsByPage(lastFamilyId, pageSize);
            case XIMI:
                return xmFamilyExtMapper.getFamilyIdsByPage(lastFamilyId, pageSize);
            default:
                log.warn("未支持的业务环境: {}", bizEnum.name());
                return Arrays.asList();
        }
    }

    /**
     * 根据业务环境获取Player ID列表
     */
    private List<Long> getPlayerIdsByBizEnum(BusinessEvnEnum bizEnum) {
        // 由于PlayerSignMapper没有直接的分页查询方法，这里模拟获取Player ID
        // 实际项目中应该根据具体的业务逻辑来获取

        switch (bizEnum) {
            case PP:
                // 模拟从PP业务获取Player ID
                return Arrays.asList(1001L, 1002L, 1003L, 1004L, 1005L);
            case HEI_YE:
                // 模拟从HEI_YE业务获取Player ID
                return Arrays.asList(2001L, 2002L, 2003L, 2004L, 2005L);
            case XIMI:
                // 模拟从XIMI业务获取Player ID
                return Arrays.asList(3001L, 3002L, 3003L, 3004L, 3005L);
            default:
                log.warn("未支持的业务环境: {}", bizEnum.name());
                return Arrays.asList();
        }
    }

    /**
     * 根据业务环境获取真实的Player签约数据（可选实现）
     */
    private List<Long> getRealPlayerIdsByBizEnum(BusinessEvnEnum bizEnum) {
        // 如果需要从真实的PlayerSign表获取数据，可以使用以下方式
        // 注意：这需要根据具体的PlayerSign表结构来实现查询逻辑

        try {
            switch (bizEnum) {
                case PP:
                    // 示例：查询PP的签约成功的Player
                    PpPlayerSign ppQuery = new PpPlayerSign();
                    ppQuery.setStatus("SIGN_SUCCEED");
                    ppQuery.setType("SIGN");
                    List<PpPlayerSign> ppPlayers = ppPlayerSignMapper.selectMany(ppQuery);
                    return ppPlayers.stream()
                        .map(PpPlayerSign::getUserId)
                        .distinct()
                        .limit(10) // 限制数量
                        .collect(Collectors.toList());

                case HEI_YE:
                    // 示例：查询HEI_YE的签约成功的Player
                    HyPlayerSign hyQuery = new HyPlayerSign();
                    hyQuery.setStatus("SIGN_SUCCEED");
                    hyQuery.setType("SIGN");
                    List<HyPlayerSign> hyPlayers = hyPlayerSignMapper.selectMany(hyQuery);
                    return hyPlayers.stream()
                        .map(HyPlayerSign::getUserId)
                        .distinct()
                        .limit(10) // 限制数量
                        .collect(Collectors.toList());

                case XIMI:
                    // 示例：查询XIMI的签约成功的Player
                    XmPlayerSign xmQuery = new XmPlayerSign();
                    xmQuery.setStatus("SIGN_SUCCEED");
                    xmQuery.setType("SIGN");
                    List<XmPlayerSign> xmPlayers = xmPlayerSignMapper.selectMany(xmQuery);
                    return xmPlayers.stream()
                        .map(XmPlayerSign::getUserId)
                        .distinct()
                        .limit(10) // 限制数量
                        .collect(Collectors.toList());

                default:
                    log.warn("未支持的业务环境: {}", bizEnum.name());
                    return Arrays.asList();
            }
        } catch (Exception e) {
            log.error("获取真实Player ID失败，使用模拟数据", e);
            return getPlayerIdsByBizEnum(bizEnum); // 降级到模拟数据
        }
    }
}
